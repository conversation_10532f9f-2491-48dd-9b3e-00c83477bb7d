{% extends "admin/change_list.html" %}
{% load static %}
{% load mathfilters %}

{% block extrahead %}
{{ block.super }}
<style>
  .dashboard-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }
  .dashboard-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 20px;
    transition: transform 0.2s;
  }
  .dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
  }
  .card-title {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
  }
  .metric-value {
    font-size: 32px;
    font-weight: bold;
    color: #3498db;
    text-align: center;
    margin: 15px 0;
  }
  .metric-label {
    text-align: center;
    color: #7f8c8d;
    font-size: 14px;
  }
  .status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
  }
  .status-active { background-color: #27ae60; }
  .status-inactive { background-color: #e74c3c; }
  .status-warning { background-color: #f39c12; }
  
  .queue-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  .queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #ecf0f1;
  }
  .queue-item:last-child {
    border-bottom: none;
  }
  .queue-name {
    font-weight: 500;
    color: #2c3e50;
  }
  .queue-count {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
  }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
  <div class="dashboard-card">
    <div class="card-title">📍 Total Locations</div>
    <div class="metric-value">{{ total_locations }}</div>
    <div class="metric-label">Configured Locations</div>
  </div>
  
  <div class="dashboard-card">
    <div class="card-title">✅ Active Queues</div>
    <div class="metric-value">{{ active_queues }}</div>
    <div class="metric-label">Currently Running</div>
  </div>
  
  <div class="dashboard-card">
    <div class="card-title">⚠️ Inactive Queues</div>
    <div class="metric-value">{{ inactive_queues }}</div>
    <div class="metric-label">Need Attention</div>
  </div>
  
  <div class="dashboard-card">
    <div class="card-title">📊 Queue Status</div>
    <ul class="queue-list">
      {% for config in queue_configs %}
      <li class="queue-item">
        <span class="queue-name">
          <span class="status-indicator status-{% if config.is_active %}active{% else %}inactive{% endif %}"></span>
          {{ config.location.name }}
        </span>
        <span class="queue-count">{{ config.pending_jobs|default:0 }}</span>
      </li>
      {% empty %}
      <li class="queue-item">No queue configurations found</li>
      {% endfor %}
    </ul>
  </div>
</div>

{{ block.super }}
{% endblock %}
