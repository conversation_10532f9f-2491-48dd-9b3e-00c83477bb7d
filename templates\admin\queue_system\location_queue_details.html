{% extends "admin/base_site.html" %}
{% load static %}
{% load humanize_age %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .location-dashboard {
        padding: 20px;
    }
    .location-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding: 24px;
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .location-info h1 {
        margin: 0 0 8px 0;
        color: #2c3e50;
        font-size: 28px;
        font-weight: 600;
    }

    .location-info p {
        margin: 0;
        color: #6c757d;
        font-size: 14px;
    }
    .worker-controls {
        display: flex;
        flex-direction: column;
        gap: 20px;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .worker-status {
        display: flex;
        justify-content: center;
    }

    .worker-count-display {
        display: flex;
        align-items: center;
        gap: 12px;
        background: white;
        padding: 16px 24px;
        border-radius: 8px;
        border: 2px solid #e9ecef;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .worker-icon {
        font-size: 24px;
    }

    .worker-numbers {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .active-count {
        font-size: 24px;
        font-weight: bold;
        color: #007cba;
        line-height: 1;
    }

    .worker-label {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: 2px;
    }

    .worker-buttons {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: center;
    }

    .button-group {
        display: flex;
        gap: 12px;
    }

    .worker-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        min-width: 120px;
        justify-content: center;
    }

    .increase-btn {
        background: #28a745;
        color: white;
    }

    .increase-btn:hover:not(:disabled) {
        background: #218838;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }

    .decrease-btn {
        background: #dc3545;
        color: white;
    }

    .decrease-btn:hover:not(:disabled) {
        background: #c82333;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
    }

    .set-max-btn {
        background: #007cba;
        color: white;
        min-width: 80px;
    }

    .set-max-btn:hover:not(:disabled) {
        background: #0056b3;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 124, 186, 0.3);
    }

    .worker-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
    }

    .btn-icon {
        font-size: 16px;
        font-weight: bold;
    }

    .max-worker-control {
        display: flex;
        align-items: center;
        gap: 8px;
        background: white;
        padding: 12px 16px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .max-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        margin: 0;
    }

    .max-input {
        width: 60px;
        padding: 6px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-align: center;
        font-size: 14px;
    }

    .worker-message {
        padding: 10px 16px;
        border-radius: 6px;
        font-size: 14px;
        text-align: left;
        display: none;
        margin-top: 12px;
        max-width: 500px;
    }

    .worker-message.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
        border-left: 4px solid #28a745;
    }

    .worker-message.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-left: 4px solid #dc3545;
    }
    .queue-section {
        margin-bottom: 30px;
    }
    .queue-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .job-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .job-table th,
    .job-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }
    .job-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
    }
    .job-table tr:hover {
        background: #f8f9fa;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }
    .status-queued { background: #fff3cd; color: #856404; }
    .status-processing { background: #d1ecf1; color: #0c5460; }
    .status-requeued { background: #e2e3e5; color: #383d41; }
    .priority-badge {
        background: #f8d7da;
        color: #721c24;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
    }
    .age-indicator {
        font-size: 12px;
        color: #666;
    }
    .age-old { color: #dc3545; font-weight: bold; }
    .btn-link {
        color: #007cba;
        text-decoration: none;
        font-size: 12px;
    }
    .btn-link:hover {
        text-decoration: underline;
    }
    .max-workers-input {
        width: 60px;
        padding: 4px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="location-dashboard">
    <!-- Location Header with Worker Controls -->
    <div class="location-header">
        <div class="location-info">
            <h1>📍 {{ location.location_name }}</h1>
            <p>Queue Details and Worker Management</p>

            <!-- Status Message positioned below location name -->
            <div id="worker-message" class="worker-message"></div>
        </div>

        <div class="worker-controls">
            <!-- Worker Status Display -->
            <div class="worker-status">
                <div class="worker-count-display">
                    <span class="worker-icon">👥</span>
                    <div class="worker-numbers">
                        <span class="active-count" id="worker-count">{{ config.active_workers }}/{{ config.max_workers }}</span>
                        <span class="worker-label">Active / Max Workers</span>
                    </div>
                </div>
            </div>

            <!-- Worker Control Buttons -->
            <div class="worker-buttons">
                <div class="button-group">
                    <button class="worker-btn decrease-btn" id="decrease-btn" onclick="adjustWorkers('decrease')"
                            {% if config.active_workers <= 0 %}disabled{% endif %}>
                        <span class="btn-icon">−</span>
                        <span class="btn-text">Stop Worker</span>
                    </button>

                    <button class="worker-btn increase-btn" id="increase-btn" onclick="adjustWorkers('increase')"
                            {% if config.active_workers >= config.max_workers %}disabled{% endif %}>
                        <span class="btn-icon">+</span>
                        <span class="btn-text">Start Worker</span>
                    </button>
                </div>

                <div class="max-worker-control">
                    <label for="maxWorkersInput" class="max-label">Max Limit:</label>
                    <input type="number" id="maxWorkersInput" class="max-input"
                           value="{{ config.max_workers }}" min="1" max="10">
                    <button class="worker-btn set-max-btn" onclick="setMaxWorkers()">
                        <span class="btn-text">Update</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Active Queue Section -->
    <div class="queue-section">
        <div class="queue-header">
            <h2>🔄 Active Queue ({{ active_jobs.count }} jobs)</h2>
            <small style="color: #666;">Jobs currently queued, processing, or requeued</small>
        </div>
        
        {% if active_jobs %}
        <table class="job-table">
            <thead>
                <tr>
                    <th>Job ID</th>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Priority</th>
                    <th>Age</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in active_jobs %}
                <tr>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">
                            #{{ job.id }}
                        </a>
                    </td>
                    <td>{{ job.order.first_name }} {{ job.order.surname }}</td>
                    <td>
                        <span class="status-badge status-{{ job.status }}">
                            {% if job.status == 'queued' %}⏳{% elif job.status == 'processing' %}🔄{% elif job.status == 'requeued' %}🔄{% endif %}
                            {{ job.get_status_display }}
                        </span>
                    </td>
                    <td>
                        {% if job.priority_flag %}
                            <span class="priority-badge">⚡ HIGH</span>
                        {% else %}
                            Normal
                        {% endif %}
                    </td>
                    <td>
                        <span class="age-indicator {% if job.age_hours > 24 %}age-old{% endif %}">
                            {{ job.age_hours|humanize_hours }}
                        </span>
                        <!-- <span class="age-indicator {% if job.age_hours > 24 %}age-old{% endif %}">
                            {% if job.age_hours < 1 %}
                                {{ job.age_hours|floatformat:0 }}m
                            {% else %}
                                {{ job.age_hours|floatformat:1 }}h
                            {% endif %}
                        </span> -->
                    </td>
                    <td>{{ job.created_at|date:"M d, H:i" }}</td>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">View</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666; background: white; border-radius: 8px;">
            ✅ No jobs in active queue
        </div>
        {% endif %}
    </div>
    
    <!-- Waiting Queue Section -->
    <div class="queue-section">
        <div class="queue-header">
            <h2>⏰ Waiting Queue ({{ waiting_jobs.count }} jobs)</h2>
            <small style="color: #666;">Jobs scheduled for future processing</small>
        </div>
        
        {% if waiting_jobs %}
        <table class="job-table">
            <thead>
                <tr>
                    <th>Job ID</th>
                    <th>Customer</th>
                    <th>Scheduled For</th>
                    <th>Priority</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in waiting_jobs %}
                <tr>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">
                            #{{ job.id }}
                        </a>
                    </td>
                    <td>{{ job.order.first_name }} {{ job.order.surname }}</td>
                    <td>{{ job.scheduled_for|date:"M d, Y H:i" }}</td>
                    <td>
                        {% if job.priority_flag %}
                            <span class="priority-badge">⚡ HIGH</span>
                        {% else %}
                            Normal
                        {% endif %}
                    </td>
                    <td>{{ job.created_at|date:"M d, H:i" }}</td>
                    <td>
                        <a href="{% url 'queue_system:job_details' job.id %}" class="btn-link">View</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666; background: white; border-radius: 8px;">
            📅 No jobs scheduled for later
        </div>
        {% endif %}
    </div>
    
    <!-- Navigation -->
    <div style="text-align: center; margin-top: 30px;">
        <a href="{% url 'queue_system:queue_overview' %}" class="btn-link">← Back to Overview</a>
        <span style="margin: 0 20px;">|</span>
        <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" class="btn-link">All Jobs</a>
    </div>
</div>

<script>
let currentActiveWorkers = {{ config.active_workers }};
let currentMaxWorkers = {{ config.max_workers }};

function showMessage(message, isSuccess = true) {
    console.log('showMessage called:', message, 'success:', isSuccess);

    const messageDiv = document.getElementById('worker-message');
    console.log('Message div found:', messageDiv);

    messageDiv.textContent = message;
    messageDiv.className = `worker-message ${isSuccess ? 'success' : 'error'}`;
    messageDiv.style.display = 'block';

    // Hide message after 4 seconds
    setTimeout(() => {
        messageDiv.style.display = 'none';
        messageDiv.className = 'worker-message';
    }, 4000);
}

function updateWorkerDisplay(activeWorkers, maxWorkers) {
    console.log('updateWorkerDisplay called:', activeWorkers, '/', maxWorkers);

    currentActiveWorkers = activeWorkers;
    currentMaxWorkers = maxWorkers;

    const workerCountElement = document.getElementById('worker-count');
    const maxWorkersInput = document.getElementById('maxWorkersInput');

    if (workerCountElement) {
        workerCountElement.textContent = `${activeWorkers}/${maxWorkers}`;
    }
    if (maxWorkersInput) {
        maxWorkersInput.value = maxWorkers;
    }

    // Update button states
    const decreaseBtn = document.getElementById('decrease-btn');
    const increaseBtn = document.getElementById('increase-btn');

    // Decrease button: disabled if no active workers
    if (decreaseBtn) {
        decreaseBtn.disabled = activeWorkers <= 0;
    }

    // Increase button: disabled if already at max workers
    if (increaseBtn) {
        increaseBtn.disabled = activeWorkers >= maxWorkers;
    }

    console.log('Display updated successfully');
}

function adjustWorkers(action) {
    console.log('adjustWorkers called with action:', action);

    const button = event.target.closest('.worker-btn');
    console.log('Button found:', button);

    if (!button) {
        console.error('Button not found!');
        return;
    }

    const btnText = button.querySelector('.btn-text');
    console.log('Text element:', btnText);

    const originalText = btnText ? btnText.textContent : '';

    button.disabled = true;
    if (btnText) {
        btnText.textContent = action === 'increase' ? 'Starting...' : 'Stopping...';
    }

    const url = '{% url "queue_system:adjust_workers" location.id %}';
    console.log('Making request to:', url);

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: 'action=' + action
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            updateWorkerDisplay(data.active_workers, data.max_workers);
            showMessage(data.message, true);
        } else {
            showMessage(data.message || 'Operation failed', false);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Network error: ' + error.message, false);
    })
    .finally(() => {
        // Reset button
        button.disabled = false;
        if (btnText && originalText) {
            btnText.textContent = originalText;
        }

        // Update button states
        updateWorkerDisplay(currentActiveWorkers, currentMaxWorkers);
    });
}

function setMaxWorkers() {
    const maxWorkers = document.getElementById('maxWorkersInput').value;
    const button = event.target.closest('.worker-btn');
    const btnText = button.querySelector('.btn-text');

    button.disabled = true;
    btnText.textContent = 'Updating...';

    fetch('{% url "queue_system:adjust_workers" location.id %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: 'action=set_max&max_workers=' + maxWorkers
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateWorkerDisplay(data.active_workers, data.max_workers);
            showMessage(data.message, true);
        } else {
            showMessage(data.message || 'Operation failed', false);
        }
    })
    .catch(error => {
        showMessage('Network error: ' + error.message, false);
    })
    .finally(() => {
        button.disabled = false;
        btnText.textContent = 'Update';
    });
}

function updateLiveStats() {
    fetch('{% url "queue_system:live_stats_location" location.id %}')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const location = data.location;

            // Update worker count
            updateWorkerDisplay(location.active_workers, location.max_workers);

            // Update job counts
            document.querySelector('.queued').textContent = location.queued;
            document.querySelector('.processing').textContent = location.processing;
            document.querySelector('.completed').textContent = location.completed;
            document.querySelector('.failed').textContent = location.failed;
            document.querySelector('.review').textContent = location.review;

            // Update recent activity if element exists
            const activityList = document.getElementById('recent-activity');
            if (activityList && location.recent_activity) {
                activityList.innerHTML = '';
                location.recent_activity.forEach(activity => {
                    const li = document.createElement('li');
                    li.innerHTML = `
                        <span class="activity-time">${activity.updated_at}</span>
                        <span class="activity-status status-${activity.status}">${activity.status}</span>
                        <span class="activity-customer">${activity.customer_name}</span>
                        <a href="/queue/admin/job-details/${activity.id}/" class="activity-link">View</a>
                    `;
                    activityList.appendChild(li);
                });
            }
        }
    })
    .catch(error => {
        console.log('Live update failed:', error);
    });
}

// Start live updates every 5 seconds for real-time sync
setInterval(updateLiveStats, 5000);

// Initial update after 1 second
setTimeout(updateLiveStats, 1000);
</script>
{% endblock %}
