{% extends "admin/change_list.html" %}
{% load static %}
{% load mathfilters %}

{% block extrahead %}
{{ block.super }}
<style>
  .error-dashboard {
    margin-bottom: 30px;
  }
  /* Style definitions remain unchanged */
</style>
{% endblock %}

{% block content %}
<div class="error-dashboard">
  <!-- Content remains unchanged -->
  
  <div class="error-chart">
    <div class="chart-title">Error Trend (Last 7 Days)</div>
    <div class="chart-container">
      {% for day in error_trend %}
      <div class="chart-bar" style="height: {% if error_trend_max %}{{ day.count|mul:100|div:error_trend_max }}{% else %}0{% endif %}%; min-height: 5px;">
        <div class="chart-value">{{ day.count }}</div>
        <div class="chart-label">{{ day.date|date:"D" }}</div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>

{{ block.super }}
{% endblock %}


