{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .dashboard-container {
        padding: 20px;
    }
    .overview-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stat-number {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 8px;
    }
    .stat-label {
        font-size: 14px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    .locations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 20px;
    }
    .location-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .location-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .location-name {
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }
    .success-rate {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }
    .success-high { background: #d4edda; color: #155724; }
    .success-medium { background: #fff3cd; color: #856404; }
    .success-low { background: #f8d7da; color: #721c24; }
    .queue-metrics {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-bottom: 15px;
    }
    .metric {
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
    }
    .metric-value {
        font-size: 20px;
        font-weight: bold;
        color: #007cba;
    }
    .metric-label {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
    }
    .worker-status {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        margin-bottom: 10px;
    }
    .recent-activity {
        display: flex;
        justify-content: space-around;
        font-size: 12px;
    }
    .activity-item {
        text-align: center;
    }
    .activity-number {
        font-size: 16px;
        font-weight: bold;
    }
    .completed { color: #28a745; }
    .failed { color: #dc3545; }
    .btn-link {
        color: #007cba;
        text-decoration: none;
        font-weight: 500;
    }
    .btn-link:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <h1>📊 Queue Overview Dashboard</h1>
    
    <!-- Overall System Statistics -->
    <div class="overview-stats">
        <div class="stat-card">
            <div class="stat-number" style="color: #007cba;">{{ overall_stats.total_active_jobs }}</div>
            <div class="stat-label">Active Jobs</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #ffc107;">{{ overall_stats.total_waiting_jobs }}</div>
            <div class="stat-label">Waiting Jobs</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #28a745;">{{ overall_stats.total_active_workers }}</div>
            <div class="stat-label">Active Workers</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #6c757d;">{{ overall_stats.total_max_workers }}</div>
            <div class="stat-label">Max Workers</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #17a2b8;">{{ overall_stats.worker_utilization }}%</div>
            <div class="stat-label">Worker Utilization</div>
        </div>
    </div>
    
    <!-- Location-Specific Statistics -->
    <h2>📍 Location Queue Status</h2>
    <div class="locations-grid">
        {% for stat in location_stats %}
        <div class="location-card">
            <div class="location-header">
                <div class="location-name">{{ stat.location.location_name }}</div>
                <div class="success-rate {% if stat.success_rate >= 80 %}success-high{% elif stat.success_rate >= 60 %}success-medium{% else %}success-low{% endif %}">
                    {{ stat.success_rate }}% Success
                </div>
            </div>
            
            <!-- Queue Metrics -->
            <div class="queue-metrics">
                <div class="metric">
                    <div class="metric-value active-queue" data-location="{{ stat.location.id }}">{{ stat.active_queue_length }}</div>
                    <div class="metric-label">Active Queue</div>
                </div>
                <div class="metric">
                    <div class="metric-value waiting-queue" data-location="{{ stat.location.id }}">{{ stat.waiting_queue_length }}</div>
                    <div class="metric-label">Waiting Queue</div>
                </div>
            </div>
            
            <!-- Worker Status -->
            <div class="worker-status">
                <span>👥 Workers: <span class="worker-count" data-location="{{ stat.location.id }}">{{ stat.active_workers }}/{{ stat.max_workers }}</span></span>
                <a href="{% url 'queue_system:location_queue_details' stat.location.id %}" class="btn-link">
                    View Details →
                </a>
            </div>
            
            <!-- Recent Activity (Last 24 Hours) -->
            <div class="recent-activity">
                <div class="activity-item">
                    <div class="activity-number completed">{{ stat.recent_completed }}</div>
                    <div>Completed</div>
                </div>
                <div class="activity-item">
                    <div class="activity-number failed">{{ stat.recent_failed }}</div>
                    <div>Failed</div>
                </div>
                <div class="activity-item">
                    <div class="activity-number">{{ stat.total_jobs_week }}</div>
                    <div>Total (7d)</div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="location-card">
            <p>No locations configured yet.</p>
        </div>
        {% endfor %}
    </div>
    
    <!-- Configuration Summary Cards -->
    <div class="config-summary" style="margin-top: 30px;">
        <h3 style="margin-bottom: 20px; color: #417690;">⚙️ Configuration Summary</h3>
        <div class="config-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div class="config-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div class="config-title" style="font-weight: bold; color: #2c3e50; margin-bottom: 15px;">📍 Total Locations</div>
                <div class="config-value" style="font-size: 32px; font-weight: bold; color: #3498db; text-align: center;">{{ config_stats.total_locations }}</div>
                <div class="config-label" style="text-align: center; color: #7f8c8d; font-size: 14px;">Configured Locations</div>
            </div>

            <div class="config-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div class="config-title" style="font-weight: bold; color: #2c3e50; margin-bottom: 15px;">⏱️ Time Window Locations</div>
                <div class="config-value" style="font-size: 32px; font-weight: bold; color: #f39c12; text-align: center;">{{ config_stats.time_window_locations }}</div>
                <div class="config-label" style="text-align: center; color: #7f8c8d; font-size: 14px;">Using Scheduled Processing</div>
            </div>

            <div class="config-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div class="config-title" style="font-weight: bold; color: #2c3e50; margin-bottom: 15px;">🤖 Worker Utilization</div>
                <div class="config-value" style="font-size: 32px; font-weight: bold; color: #27ae60; text-align: center;">{{ overall_stats.total_active_workers }} / {{ overall_stats.total_max_workers }}</div>
                <div class="config-label" style="text-align: center; color: #7f8c8d; font-size: 14px;">Active Workers vs Capacity</div>
                <div style="background: #ecf0f1; border-radius: 10px; height: 8px; margin-top: 10px;">
                    <div style="background: #27ae60; height: 8px; border-radius: 10px; width: {% if overall_stats.total_max_workers > 0 %}{{ overall_stats.total_active_workers|mul:100|div:overall_stats.total_max_workers }}{% else %}0{% endif %}%;"></div>
                </div>
            </div>

            <div class="config-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <div class="config-title" style="font-weight: bold; color: #2c3e50; margin-bottom: 15px;">🔧 Auto-Scale Enabled</div>
                <div class="config-value" style="font-size: 32px; font-weight: bold; color: #8e44ad; text-align: center;">{{ config_stats.auto_scale_locations }}</div>
                <div class="config-label" style="text-align: center; color: #7f8c8d; font-size: 14px;">Locations with Auto-Scaling</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div style="margin-top: 30px; text-align: center;">
        <a href="{% url 'queue_system:review_queue_dashboard' %}" class="btn-link" style="margin-right: 20px;">
            🔍 Review Queue Dashboard
        </a>
        <a href="{% url 'admin:queue_system_queuedjob_changelist' %}" class="btn-link">
            📋 All Jobs
        </a>
    </div>
</div>

<!-- <script>
// WebSocket connection setup
let socket;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 5000; // 5 seconds

function connectWebSocket() {
    const wsScheme = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
    const wsPath = wsScheme + window.location.host + '/ws/queue/updates/';
    
    socket = new WebSocket(wsPath);

    socket.onopen = function(e) {
        console.log('WebSocket connection established');
        reconnectAttempts = 0;
        updateConnectionStatus('🟢 Live', '#28a745');
    };

    socket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        if (data.type === 'initial_data' || data.type === 'queue_update') {
            updateDashboard(data.data);
        }
    };

    socket.onclose = function(event) {
        if (event.wasClean) {
            console.log(`WebSocket connection closed cleanly, code=${event.code}, reason=${event.reason}`);
        } else {
            console.error('WebSocket connection died');
        }
        
        updateConnectionStatus('🔴 Offline', '#dc3545');
        
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            reconnectAttempts++;
            setTimeout(connectWebSocket, RECONNECT_DELAY);
        }
    };

    socket.onerror = function(error) {
        console.error('WebSocket error:', error);
        updateConnectionStatus('⚠️ Error', '#fd7e14');
    };
}

function updateDashboard(overview) {
    // Update overall stats
    document.querySelectorAll('.stat-card .stat-number').forEach((el, index) => {
        const values = [
            overview.total_active_jobs,
            overview.total_waiting_jobs,
            overview.total_active_workers,
            overview.total_max_workers,
            overview.total_active_workers > 0 ? 
                Math.round((overview.total_active_workers / overview.total_max_workers) * 100) + '%' : '0%'
        ];
        if (values[index]) el.textContent = values[index];
    });

    // Update location-specific stats
    overview.locations.forEach(location => {
        const workerCountEl = document.querySelector(`.worker-count[data-location="${location.id}"]`);
        if (workerCountEl) {
            workerCountEl.textContent = `${location.active_workers}/${location.max_workers}`;
        }

        const activeQueueEl = document.querySelector(`.active-queue[data-location="${location.id}"]`);
        const waitingQueueEl = document.querySelector(`.waiting-queue[data-location="${location.id}"]`);

        if (activeQueueEl) activeQueueEl.textContent = location.processing;
        if (waitingQueueEl) waitingQueueEl.textContent = location.queued;

        // Update success rate if element exists
        const successRateEl = document.querySelector(`.success-rate[data-location="${location.id}"]`);
        if (successRateEl) {
            successRateEl.textContent = `${location.success_rate}% Success`;
            successRateEl.className = `success-rate ${
                location.success_rate >= 80 ? 'success-high' :
                location.success_rate >= 60 ? 'success-medium' :
                'success-low'
            }`;
        }
    });

    // Flash update indicator
    const indicator = document.getElementById('live-indicator');
    if (indicator) {
        indicator.style.color = '#28a745';
        indicator.textContent = '🟢 Live';
        setTimeout(() => {
            indicator.style.color = '#6c757d';
            indicator.textContent = '⚪ Live';
        }, 1000);
    }
}

function updateConnectionStatus(text, color) {
    const indicator = document.getElementById('live-indicator');
    if (indicator) {
        indicator.textContent = text;
        indicator.style.color = color;
    }
}

// Initialize WebSocket connection when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Add live indicator
    const header = document.querySelector('h1');
    if (header && !document.getElementById('live-indicator')) {
        const indicator = document.createElement('span');
        indicator.id = 'live-indicator';
        indicator.style.marginLeft = '15px';
        indicator.style.fontSize = '14px';
        indicator.style.verticalAlign = 'middle';
        indicator.textContent = '⚪ Connecting...';
        header.appendChild(indicator);
    }
    
    // Start WebSocket connection
    connectWebSocket();
    
    // Clean up on page unload
    window.addEventListener('beforeunload', function() {
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.close();
        }
    });
});
</script> -->
{% endblock %}
